"""
轻量级监控器主模块

提供基础的系统监控功能，资源占用最小化
"""

import os
import time
import threading
import logging
import sqlite3
import psutil
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from pathlib import Path

from .config import MonitoringConfig
from .metrics import MetricsCollector
from .alerts import AlertManager


class LightweightMonitor:
    """轻量级监控器"""
    
    def __init__(self, config: Optional[MonitoringConfig] = None, app=None):
        """初始化监控器"""
        self.config = config or MonitoringConfig.from_env()
        self.app = app
        self.logger = self._setup_logger()
        
        # 验证配置
        config_errors = self.config.validate()
        if config_errors:
            self.logger.error(f"监控配置错误: {', '.join(config_errors)}")
            if not self.config.enabled:
                return
        
        # 初始化组件
        self.metrics_collector = MetricsCollector(self.config, self.logger)
        self.alert_manager = AlertManager(self.config, self.logger, self.app)
        
        # 运行状态
        self._running = False
        self._threads = []
        
        # 性能统计
        self._start_time = time.time()
        self._last_metrics_time = 0
        self._last_health_check_time = 0
        
        self.logger.info(f"轻量级监控器初始化完成，级别: {self.config.level}")
    
    def _setup_logger(self) -> logging.Logger:
        """设置监控专用日志器"""
        logger = logging.getLogger('monitoring')
        logger.setLevel(logging.INFO)

        # 避免重复添加处理器
        if logger.handlers:
            return logger

        # 创建日志目录 - 支持自定义日志目录
        log_dir_env = os.getenv('MONITORING_LOG_DIR', 'logs')
        log_dir = Path(log_dir_env)
        log_dir.mkdir(exist_ok=True)

        # 文件处理器 - 使用可写的日志目录
        log_file = log_dir / 'monitoring.log'
        try:
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.INFO)
        except PermissionError:
            # 如果无法写入默认日志目录，使用临时目录
            import tempfile
            temp_log_dir = Path(tempfile.gettempdir()) / 'tempmail_monitoring'
            temp_log_dir.mkdir(exist_ok=True)
            log_file = temp_log_dir / 'monitoring.log'
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.INFO)
            logger.warning(f"无法写入默认日志目录，使用临时目录: {log_file}")
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        return logger
    
    def start(self):
        """启动监控"""
        if not self.config.enabled:
            self.logger.info("监控已禁用，跳过启动")
            return
        
        if self._running:
            self.logger.warning("监控已在运行中")
            return
        
        self._running = True
        self.logger.info("启动轻量级监控器...")
        
        # 启动指标收集线程
        metrics_thread = threading.Thread(
            target=self._metrics_loop,
            name="MonitoringMetrics",
            daemon=True
        )
        metrics_thread.start()
        self._threads.append(metrics_thread)
        
        # 启动健康检查线程
        health_thread = threading.Thread(
            target=self._health_check_loop,
            name="MonitoringHealth",
            daemon=True
        )
        health_thread.start()
        self._threads.append(health_thread)
        
        # 启动清理线程
        cleanup_thread = threading.Thread(
            target=self._cleanup_loop,
            name="MonitoringCleanup",
            daemon=True
        )
        cleanup_thread.start()
        self._threads.append(cleanup_thread)
        
        self.logger.info(f"监控器已启动，运行 {len(self._threads)} 个后台线程")
    
    def stop(self):
        """停止监控"""
        if not self._running:
            return
        
        self.logger.info("停止监控器...")
        self._running = False
        
        # 等待线程结束（最多等待5秒）
        for thread in self._threads:
            thread.join(timeout=5)
        
        self.logger.info("监控器已停止")
    
    def _metrics_loop(self):
        """指标收集循环"""
        while self._running:
            try:
                start_time = time.time()
                
                # 收集指标
                metrics = self.metrics_collector.collect_all()
                
                # 检查告警
                self.alert_manager.check_alerts(metrics)
                
                # 记录性能
                collection_time = time.time() - start_time
                self._last_metrics_time = collection_time
                
                if collection_time > 1.0:  # 如果收集时间超过1秒，记录警告
                    self.logger.warning(f"指标收集耗时较长: {collection_time:.2f}秒")
                
                # 等待下次收集
                time.sleep(self.config.metrics_interval)
                
            except Exception as e:
                self.logger.error(f"指标收集循环异常: {e}", exc_info=True)
                time.sleep(min(self.config.metrics_interval, 60))  # 异常时最多等待60秒
    
    def _health_check_loop(self):
        """健康检查循环"""
        while self._running:
            try:
                start_time = time.time()
                
                # 执行健康检查
                health_status = self._perform_health_check()
                
                # 如果健康检查失败，发送告警
                if not health_status['healthy']:
                    self.alert_manager.send_alert(
                        'health_check_failed',
                        f"健康检查失败: {health_status['message']}",
                        'critical',
                        health_status
                    )
                
                # 记录性能
                check_time = time.time() - start_time
                self._last_health_check_time = check_time
                
                # 等待下次检查
                time.sleep(self.config.health_check_interval)
                
            except Exception as e:
                self.logger.error(f"健康检查循环异常: {e}", exc_info=True)
                time.sleep(min(self.config.health_check_interval, 300))
    
    def _cleanup_loop(self):
        """清理循环"""
        while self._running:
            try:
                # 每小时执行一次清理
                time.sleep(3600)
                
                if not self._running:
                    break
                
                self._cleanup_old_data()
                
            except Exception as e:
                self.logger.error(f"清理循环异常: {e}", exc_info=True)
    
    def _perform_health_check(self) -> Dict[str, Any]:
        """执行健康检查"""
        try:
            # 检查数据库连接
            if self.app:
                with self.app.app_context():
                    from app import get_db_connection, DATABASE_ADAPTER_AVAILABLE
                    import os

                    # 检查数据库类型并使用相应的连接方法
                    db_type = os.getenv('DATABASE_TYPE', 'sqlite').lower()

                    if DATABASE_ADAPTER_AVAILABLE and db_type == 'mysql':
                        # MySQL连接 - 使用SQLAlchemy
                        with get_db_connection() as conn:
                            from sqlalchemy import text
                            result = conn.execute(text("SELECT 1"))
                            result.fetchone()
                    else:
                        # SQLite连接 - 使用传统cursor方法
                        with get_db_connection() as conn:
                            cursor = conn.cursor()
                            cursor.execute("SELECT 1")
                            cursor.fetchone()
            
            # 检查关键文件
            critical_files = ['app.py', 'requirements.txt']
            for file_path in critical_files:
                if not os.path.exists(file_path):
                    return {
                        'healthy': False,
                        'message': f'关键文件缺失: {file_path}',
                        'timestamp': datetime.now().isoformat()
                    }
            
            # 检查日志目录
            log_dir = Path('logs')
            if not log_dir.exists():
                return {
                    'healthy': False,
                    'message': '日志目录不存在',
                    'timestamp': datetime.now().isoformat()
                }
            
            return {
                'healthy': True,
                'message': '所有检查通过',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'healthy': False,
                'message': f'健康检查异常: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    def _cleanup_old_data(self):
        """清理过期数据"""
        try:
            # 清理指标数据
            self.metrics_collector.cleanup_old_metrics()
            
            # 清理日志文件
            self._cleanup_log_files()
            
            self.logger.info("数据清理完成")
            
        except Exception as e:
            self.logger.error(f"数据清理异常: {e}", exc_info=True)
    
    def _cleanup_log_files(self):
        """清理过期日志文件"""
        log_dir = Path('logs')
        if not log_dir.exists():
            return
        
        cutoff_date = datetime.now() - timedelta(days=self.config.log_retention_days)
        
        for log_file in log_dir.glob('*.log'):
            try:
                file_mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
                if file_mtime < cutoff_date:
                    log_file.unlink()
                    self.logger.info(f"删除过期日志文件: {log_file}")
            except Exception as e:
                self.logger.warning(f"删除日志文件失败 {log_file}: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取监控器状态"""
        uptime = time.time() - self._start_time
        
        return {
            'enabled': self.config.enabled,
            'running': self._running,
            'uptime_seconds': uptime,
            'level': self.config.level,
            'threads_count': len(self._threads),
            'last_metrics_time': self._last_metrics_time,
            'last_health_check_time': self._last_health_check_time,
            'config': {
                'metrics_interval': self.config.metrics_interval,
                'health_check_interval': self.config.health_check_interval,
                'max_memory_mb': self.config.max_memory_mb
            }
        }
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前指标"""
        if not self._running:
            return {'error': '监控器未运行'}
        
        return self.metrics_collector.get_latest_metrics()
    
    def force_health_check(self) -> Dict[str, Any]:
        """强制执行健康检查"""
        return self._perform_health_check()
