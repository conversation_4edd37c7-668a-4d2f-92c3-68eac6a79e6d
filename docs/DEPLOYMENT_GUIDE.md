# 临时邮箱系统部署指南

本指南提供了在新服务器环境中部署临时邮箱系统的完整步骤。

## 📋 目录

- [系统要求](#系统要求)
- [快速部署](#快速部署)
- [手动部署](#手动部署)
- [Docker部署](#docker部署)
- [配置说明](#配置说明)
- [故障排除](#故障排除)
- [维护操作](#维护操作)

## 🖥️ 系统要求

### 最低要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **CPU**: 1核心
- **内存**: 512MB
- **存储**: 2GB可用空间
- **网络**: 公网IP地址（用于接收邮件）

### 推荐配置
- **操作系统**: Ubuntu 22.04 LTS
- **CPU**: 2核心
- **内存**: 1GB
- **存储**: 10GB SSD
- **网络**: 稳定的公网连接

### 软件依赖
- Python 3.8+
- Node.js 16+
- Nginx
- SQLite3 (默认) 或 MySQL 8.0+
- Git

## 🚀 快速部署

### 使用自动化脚本部署

1. **下载项目代码**
```bash
git clone <your-repository-url>
cd tempmail
```

2. **运行自动化部署脚本**
```bash
sudo ./scripts/deploy.sh --domain your-domain.com --env production
```

3. **启动服务**
```bash
sudo ./scripts/manage-service.sh start
```

4. **验证部署**
```bash
curl http://your-domain.com/health
```

### 脚本选项说明

```bash
# 基本部署
sudo ./scripts/deploy.sh --domain example.com

# 自定义用户和目录
sudo ./scripts/deploy.sh --user myuser --dir /opt/tempmail

# 跳过某些步骤
sudo ./scripts/deploy.sh --skip-nginx --skip-systemd

# 开发环境部署
sudo ./scripts/deploy.sh --env development --domain localhost
```

## 🔧 手动部署

### 1. 系统准备

#### 更新系统
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
# 或
sudo dnf update -y
```

#### 安装基础软件
```bash
# Ubuntu/Debian
sudo apt install -y python3 python3-pip python3-venv nginx sqlite3 \
    curl wget git build-essential python3-dev libssl-dev libffi-dev \
    nodejs npm

# CentOS/RHEL
sudo yum install -y python3 python3-pip nginx sqlite curl wget git \
    gcc python3-devel openssl-devel libffi-devel nodejs npm
```

### 2. 创建用户和目录

```bash
# 创建系统用户
sudo groupadd tempmail
sudo useradd -r -g tempmail -d /var/www/tempmail -s /bin/bash tempmail

# 创建目录结构
sudo mkdir -p /var/www/tempmail/{logs,database,run,static}
sudo mkdir -p /etc/tempmail
sudo chown -R tempmail:tempmail /var/www/tempmail
```

### 3. 部署应用程序

```bash
# 切换到项目目录
cd /var/www/tempmail

# 复制项目文件
sudo cp -r /path/to/source/* .
sudo chown -R tempmail:tempmail .

# 创建Python虚拟环境
sudo -u tempmail python3 -m venv venv
sudo -u tempmail bash -c "source venv/bin/activate && pip install -r requirements.txt"

# 安装前端依赖
sudo -u tempmail npm install
sudo -u tempmail npm run build-css
```

### 4. 配置环境变量

```bash
# 复制环境变量模板
sudo cp config/env/.env.production .env

# 编辑配置文件
sudo nano .env
```

**重要配置项**:
```bash
# 域名配置
DOMAIN_NAME=your-domain.com

# 数据库配置
DATABASE_TYPE=sqlite
DATABASE_PATH=/var/www/tempmail/database/tempmail.db

# 安全密钥 (请生成新的密钥)
SECRET_KEY=your-secure-secret-key-here

# 监控配置
MONITORING_ALERT_EMAIL_TO=<EMAIL>
```

### 5. 初始化数据库

```bash
sudo -u tempmail bash -c "
cd /var/www/tempmail
source venv/bin/activate
python -c '
from app import app
with app.app_context():
    from app import init_db_schema
    init_db_schema()
    print(\"数据库初始化完成\")
'
"
```

### 6. 配置Systemd服务

```bash
# 复制服务文件
sudo cp config/systemd/tempmail.service /etc/systemd/system/

# 重新加载systemd
sudo systemctl daemon-reload

# 启用并启动服务
sudo systemctl enable tempmail
sudo systemctl start tempmail

# 检查服务状态
sudo systemctl status tempmail
```

### 7. 配置Nginx

```bash
# 复制Nginx配置
sudo cp config/nginx/tempmail.conf /etc/nginx/sites-available/tempmail

# 编辑配置文件，替换域名
sudo sed -i 's/your-domain.com/actual-domain.com/g' /etc/nginx/sites-available/tempmail

# 启用站点
sudo ln -s /etc/nginx/sites-available/tempmail /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## 🐳 Docker部署

### 1. 安装Docker

```bash
# Ubuntu
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 使用Docker部署

```bash
# 克隆项目
git clone <your-repository-url>
cd tempmail

# 生产环境部署
./scripts/docker-deploy.sh --env production --domain your-domain.com up

# 开发环境部署
./scripts/docker-deploy.sh --env development up
```

### 3. Docker管理命令

```bash
# 查看状态
./scripts/docker-deploy.sh status

# 查看日志
./scripts/docker-deploy.sh logs tempmail

# 重启服务
./scripts/docker-deploy.sh restart

# 停止服务
./scripts/docker-deploy.sh down

# 清理资源
./scripts/docker-deploy.sh clean
```

## ⚙️ 配置说明

### 环境变量配置

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `DOMAIN_NAME` | 服务域名 | `localhost` |
| `DATABASE_TYPE` | 数据库类型 | `sqlite` |
| `EMAIL_EXPIRATION_HOURS` | 邮箱有效期 | `24` |
| `MONITORING_LEVEL` | 监控级别 | `basic` |
| `GUNICORN_WORKERS` | 工作进程数 | `2` |

### Gunicorn配置

主要配置文件: `gunicorn.conf.py`

```python
# 基本配置
bind = "0.0.0.0:8080"
workers = 2
timeout = 30

# 日志配置
accesslog = "logs/gunicorn_access.log"
errorlog = "logs/gunicorn_error.log"
```

### Nginx配置

主要配置文件: `config/nginx/tempmail.conf`

- 静态文件服务
- API代理
- SSL终止
- 安全头设置
- 速率限制

## 🔍 故障排除

### 常见问题

#### 1. 服务启动失败

```bash
# 检查服务状态
sudo systemctl status tempmail

# 查看详细日志
sudo journalctl -u tempmail -f

# 检查配置文件
sudo -u tempmail bash -c "cd /var/www/tempmail && source venv/bin/activate && python -c 'from app import app; print(\"配置正常\")'"
```

#### 2. 端口被占用

```bash
# 检查端口占用
sudo lsof -i :8080

# 杀死占用进程
sudo kill -9 <PID>
```

#### 3. 权限问题

```bash
# 修复文件权限
sudo chown -R tempmail:tempmail /var/www/tempmail
sudo chmod -R 755 /var/www/tempmail
sudo chmod 600 /var/www/tempmail/.env
```

#### 4. 数据库问题

```bash
# 检查数据库文件
ls -la /var/www/tempmail/database/

# 重新初始化数据库
sudo -u tempmail bash -c "cd /var/www/tempmail && source venv/bin/activate && python -c 'from app import app; app.app_context().push(); from app import init_db_schema; init_db_schema()'"
```

### 日志文件位置

- **应用程序日志**: `/var/www/tempmail/logs/app.log`
- **Gunicorn访问日志**: `/var/www/tempmail/logs/gunicorn_access.log`
- **Gunicorn错误日志**: `/var/www/tempmail/logs/gunicorn_error.log`
- **Nginx访问日志**: `/var/log/nginx/tempmail_access.log`
- **Nginx错误日志**: `/var/log/nginx/tempmail_error.log`
- **系统日志**: `sudo journalctl -u tempmail`

## 🛠️ 维护操作

### 服务管理

```bash
# 启动服务
sudo ./scripts/manage-service.sh start

# 停止服务
sudo ./scripts/manage-service.sh stop

# 重启服务
sudo ./scripts/manage-service.sh restart

# 查看状态
sudo ./scripts/manage-service.sh status

# 查看日志
sudo ./scripts/manage-service.sh logs

# 健康检查
sudo ./scripts/manage-service.sh health
```

### 备份和恢复

```bash
# 备份数据库
cp /var/www/tempmail/database/tempmail.db /backup/tempmail_$(date +%Y%m%d).db

# 备份配置
sudo ./scripts/manage-service.sh backup

# 恢复数据库
cp /backup/tempmail_20231201.db /var/www/tempmail/database/tempmail.db
sudo chown tempmail:tempmail /var/www/tempmail/database/tempmail.db
```

### 更新应用程序

```bash
# 停止服务
sudo ./scripts/manage-service.sh stop

# 备份当前版本
sudo cp -r /var/www/tempmail /backup/tempmail_backup_$(date +%Y%m%d)

# 更新代码
cd /var/www/tempmail
sudo -u tempmail git pull

# 更新依赖
sudo -u tempmail bash -c "source venv/bin/activate && pip install -r requirements.txt"
sudo -u tempmail npm install
sudo -u tempmail npm run build-css

# 重启服务
sudo ./scripts/manage-service.sh start
```

### 监控和性能

```bash
# 查看资源使用
htop
iotop

# 查看网络连接
netstat -tlnp | grep :8080

# 查看磁盘使用
df -h
du -sh /var/www/tempmail/*

# 清理日志
sudo find /var/www/tempmail/logs -name "*.log" -mtime +30 -delete
```

## 📞 技术支持

如果遇到问题，请按以下步骤操作：

1. 查看相关日志文件
2. 检查系统资源使用情况
3. 验证配置文件正确性
4. 参考故障排除部分
5. 如问题仍未解决，请提供详细的错误信息和系统环境

---

## 🔐 SSL/HTTPS配置

### 使用Let's Encrypt

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 手动SSL证书

```bash
# 编辑Nginx配置
sudo nano /etc/nginx/sites-available/tempmail

# 添加SSL配置
ssl_certificate /path/to/your/certificate.crt;
ssl_certificate_key /path/to/your/private.key;
```

## 📊 性能优化

### 系统级优化

```bash
# 调整文件描述符限制
echo "tempmail soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "tempmail hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# 优化内核参数
echo "net.core.somaxconn = 65535" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### 应用级优化

```bash
# 调整Gunicorn工作进程数
export GUNICORN_WORKERS=4

# 启用Nginx缓存
# 在Nginx配置中添加缓存设置
```

## 📚 相关文档

- [API文档](API_DOCUMENTATION.md)
- [开发指南](DEVELOPMENT_GUIDE.md)
- [安全配置](SECURITY_GUIDE.md)
- [性能优化](PERFORMANCE_GUIDE.md)
