# 临时邮箱系统 - 快速参考

## 🚀 一键部署

```bash
# 1. 克隆项目
git clone <your-repository-url>
cd tempmail

# 2. 自动化部署
sudo ./scripts/deploy.sh --domain testkuroneko.xyz --env production

# 3. 启动服务
sudo ./scripts/manage-service.sh start

# 4. 验证部署
curl http://your-domain.com/health
```

## 📁 重要文件位置

| 文件/目录 | 路径 | 说明 |
|-----------|------|------|
| 应用程序 | `/var/www/tempmail/` | 主程序目录 |
| 配置文件 | `/var/www/tempmail/.env` | 环境变量配置 |
| 数据库 | `/var/www/tempmail/database/` | SQLite数据库 |
| 日志文件 | `/var/www/tempmail/logs/` | 应用程序日志 |
| 服务文件 | `/etc/systemd/system/tempmail.service` | Systemd服务 |
| Nginx配置 | `/etc/nginx/sites-available/tempmail` | Web服务器配置 |

## 🛠️ 常用命令

### 服务管理
```bash
# 启动/停止/重启服务
sudo systemctl start tempmail
sudo systemctl stop tempmail
sudo systemctl restart tempmail

# 查看服务状态
sudo systemctl status tempmail

# 查看日志
sudo journalctl -u tempmail -f
```

### 使用管理脚本
```bash
# 服务操作
sudo ./scripts/manage-service.sh start|stop|restart|status

# 查看日志
sudo ./scripts/manage-service.sh logs

# 健康检查
sudo ./scripts/manage-service.sh health

# 备份配置
sudo ./scripts/manage-service.sh backup
```

### Docker部署
```bash
# 启动服务
./scripts/docker-deploy.sh --domain your-domain.com up

# 查看状态
./scripts/docker-deploy.sh status

# 查看日志
./scripts/docker-deploy.sh logs tempmail

# 停止服务
./scripts/docker-deploy.sh down
```

## 🔧 配置文件模板

### 环境变量 (.env)
```bash
# 基本配置
DOMAIN_NAME=your-domain.com
DATABASE_TYPE=sqlite
EMAIL_EXPIRATION_HOURS=24

# 安全配置
SECRET_KEY=your-secure-secret-key

# 监控配置
MONITORING_LEVEL=basic
MONITORING_ALERT_EMAIL_TO=<EMAIL>
```

### Gunicorn配置
```python
# gunicorn.conf.py
bind = "0.0.0.0:8080"
workers = 2
timeout = 30
accesslog = "logs/gunicorn_access.log"
errorlog = "logs/gunicorn_error.log"
```

## 🔍 故障排除速查

### 服务无法启动
```bash
# 检查服务状态
sudo systemctl status tempmail

# 查看详细错误
sudo journalctl -u tempmail --no-pager

# 检查端口占用
sudo lsof -i :8080

# 检查权限
sudo chown -R tempmail:tempmail /var/www/tempmail
```

### 网站无法访问
```bash
# 检查Nginx状态
sudo systemctl status nginx

# 测试Nginx配置
sudo nginx -t

# 检查防火墙
sudo ufw status
sudo firewall-cmd --list-all
```

### 数据库问题
```bash
# 检查数据库文件
ls -la /var/www/tempmail/database/

# 重新初始化数据库
cd /var/www/tempmail
sudo -u tempmail bash -c "source venv/bin/activate && python -c 'from app import app; app.app_context().push(); from app import init_db_schema; init_db_schema()'"
```

## 📊 监控检查

### 系统资源
```bash
# CPU和内存使用
htop
free -h

# 磁盘使用
df -h
du -sh /var/www/tempmail/*

# 网络连接
netstat -tlnp | grep :8080
```

### 应用程序健康
```bash
# HTTP健康检查
curl http://localhost:8080/health

# 检查进程
ps aux | grep gunicorn

# 检查日志错误
tail -f /var/www/tempmail/logs/app.log | grep ERROR
```

## 🔄 更新流程

```bash
# 1. 停止服务
sudo ./scripts/manage-service.sh stop

# 2. 备份
sudo cp -r /var/www/tempmail /backup/tempmail_$(date +%Y%m%d)

# 3. 更新代码
cd /var/www/tempmail
sudo -u tempmail git pull

# 4. 更新依赖
sudo -u tempmail bash -c "source venv/bin/activate && pip install -r requirements.txt"

# 5. 重启服务
sudo ./scripts/manage-service.sh start
```

## 🔐 安全检查清单

- [ ] 更改默认SECRET_KEY
- [ ] 配置防火墙规则
- [ ] 启用SSL/HTTPS
- [ ] 设置适当的文件权限
- [ ] 配置日志轮转
- [ ] 启用监控告警
- [ ] 定期备份数据

## 📞 紧急联系

### 快速恢复
```bash
# 重启所有服务
sudo systemctl restart tempmail nginx

# 恢复默认配置
sudo cp config/env/.env.production .env
sudo systemctl restart tempmail
```

### 日志收集
```bash
# 收集所有相关日志
mkdir -p /tmp/tempmail_logs
cp /var/www/tempmail/logs/* /tmp/tempmail_logs/
sudo journalctl -u tempmail > /tmp/tempmail_logs/systemd.log
sudo journalctl -u nginx > /tmp/tempmail_logs/nginx_systemd.log
tar -czf tempmail_logs_$(date +%Y%m%d_%H%M%S).tar.gz /tmp/tempmail_logs/
```

## 🌐 端口和URL

| 服务 | 端口 | URL | 说明 |
|------|------|-----|------|
| 应用程序 | 8080 | http://localhost:8080 | 主应用 |
| Nginx | 80/443 | http://your-domain.com | Web服务器 |
| 健康检查 | - | /health | 健康状态 |
| API | - | /api/* | REST API |

---

💡 **提示**: 将此文档保存为书签，以便快速查找常用命令和配置。
