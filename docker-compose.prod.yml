version: '3.8'

# 生产环境Docker Compose配置
# 使用方法: docker-compose -f docker-compose.prod.yml up -d

services:
  # 临时邮箱应用程序
  tempmail:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: tempmail-app-prod
    restart: always
    ports:
      - "127.0.0.1:8080:8080"  # 仅绑定到本地接口
    environment:
      - FLASK_ENV=production
      - FLASK_DEBUG=0
      - DATABASE_TYPE=sqlite
      - DATABASE_PATH=/app/database/tempmail.db
      - DOMAIN_NAME=${DOMAIN_NAME}
      - EMAIL_EXPIRATION_HOURS=24
      - MONITORING_LEVEL=basic
      - MONITORING_EMAIL_ENABLED=true
      - MONITORING_USE_INTERNAL_MAIL=true
      - GUNICORN_WORKERS=2
      - GUNICORN_TIMEOUT=30
    volumes:
      - /var/lib/tempmail/data:/app/database
      - /var/log/tempmail:/app/logs
      - /etc/tempmail/.env:/app/.env:ro
    networks:
      - tempmail-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /app/run
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID

  # Nginx反向代理 (生产环境)
  nginx:
    image: nginx:alpine
    container_name: tempmail-nginx-prod
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /etc/nginx/sites-available/tempmail:/etc/nginx/conf.d/default.conf:ro
      - /var/www/tempmail/static:/var/www/tempmail/static:ro
      - /var/log/nginx:/var/log/nginx
      - /etc/letsencrypt:/etc/letsencrypt:ro
    networks:
      - tempmail-prod
    depends_on:
      - tempmail
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  tempmail-prod:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
