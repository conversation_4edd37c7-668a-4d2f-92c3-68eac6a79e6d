# 简化版Nginx配置 - 临时邮箱系统
# 适用于开发环境或简单部署

server {
    listen 80;
    server_name your-domain.com www.your-domain.com localhost;
    
    # 日志配置
    access_log /var/log/nginx/tempmail_access.log;
    error_log /var/log/nginx/tempmail_error.log;
    
    # 根目录
    root /var/www/tempmail;
    
    # 客户端配置
    client_max_body_size 1M;
    
    # 静态文件服务
    location /static/ {
        alias /var/www/tempmail/static/;
        expires 1d;
        add_header Cache-Control "public";
    }
    
    # API端点代理
    location /api/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 健康检查
    location = /health {
        proxy_pass http://127.0.0.1:8080;
        access_log off;
    }
    
    # 主页面代理
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 隐藏敏感文件
    location ~ /\. {
        deny all;
    }
}
