# Nginx配置文件 - 临时邮箱系统
# 使用方法: 将此文件复制到 /etc/nginx/sites-available/ 并创建软链接到 sites-enabled/

# 上游服务器配置
upstream tempmail_backend {
    server 127.0.0.1:8080 max_fails=3 fail_timeout=30s;
    # 如果有多个实例，可以添加更多服务器
    # server 127.0.0.1:8081 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# 速率限制配置
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=email_fetch:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=general:10m rate=20r/s;

# 主服务器配置
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS服务器配置
server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL证书配置 (请替换为您的证书路径)
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # 安全头设置
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self';" always;
    
    # 日志配置
    access_log /var/log/nginx/tempmail_access.log;
    error_log /var/log/nginx/tempmail_error.log;
    
    # 根目录配置
    root /var/www/tempmail;
    index index.html;
    
    # 客户端配置
    client_max_body_size 1M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # 静态文件服务
    location /static/ {
        alias /var/www/tempmail/static/;
        expires 30d;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options "nosniff";
        
        # 安全设置
        location ~* \.(js|css)$ {
            add_header Content-Type text/plain;
        }
    }
    
    # Favicon
    location = /favicon.ico {
        alias /var/www/tempmail/static/favicon.ico;
        expires 7d;
        add_header Cache-Control "public";
        log_not_found off;
    }
    
    # Robots.txt
    location = /robots.txt {
        alias /var/www/tempmail/static/robots.txt;
        expires 7d;
        add_header Cache-Control "public";
        log_not_found off;
    }
    
    # API端点 - 生成邮箱地址
    location = /api/generate-address {
        limit_req zone=api_limit burst=5 nodelay;
        
        proxy_pass http://tempmail_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_buffering off;
        
        # CORS设置
        add_header Access-Control-Allow-Origin "$http_origin" always;
        add_header Access-Control-Allow-Methods "POST, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;
        add_header Access-Control-Allow-Credentials "true" always;
        
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
    
    # API端点 - 获取邮件列表
    location = /api/emails {
        limit_req zone=email_fetch burst=10 nodelay;
        
        proxy_pass http://tempmail_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_buffering off;
    }
    
    # API端点 - 获取单个邮件
    location ~ ^/api/email/(\d+)$ {
        limit_req zone=email_fetch burst=5 nodelay;
        
        proxy_pass http://tempmail_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_buffering off;
    }
    
    # 健康检查端点
    location = /health {
        proxy_pass http://tempmail_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        access_log off;
    }
    
    # 主页和其他页面
    location / {
        limit_req zone=general burst=20 nodelay;
        
        try_files $uri $uri/ @app;
    }
    
    # 代理到应用程序
    location @app {
        proxy_pass http://tempmail_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_buffering off;
    }
    
    # 安全设置 - 隐藏敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(env|conf|config|ini|log|bak|backup|old|tmp)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        internal;
        root /var/www/tempmail/templates/errors;
    }
    
    location = /50x.html {
        internal;
        root /var/www/tempmail/templates/errors;
    }
}
