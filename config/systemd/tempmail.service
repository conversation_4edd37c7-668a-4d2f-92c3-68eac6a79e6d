[Unit]
Description=Temporary Email System - Production Gunicorn WSGI Server
Documentation=https://github.com/your-repo/tempmail
After=network.target network-online.target
Wants=network-online.target
RequiresMountsFor=/var/www/tempmail

[Service]
# 服务类型和用户配置
Type=notify
User=tempmail
Group=tempmail
WorkingDirectory=/var/www/tempmail

# 环境变量配置
Environment=PATH=/var/www/tempmail/venv/bin:/usr/local/bin:/usr/bin:/bin
Environment=PYTHONPATH=/var/www/tempmail
Environment=FLASK_APP=app.py
Environment=FLASK_ENV=production
Environment=FLASK_DEBUG=0
Environment=GUNICORN_WORKERS=2
Environment=GUNICORN_BIND=0.0.0.0:8080
Environment=GUNICORN_TIMEOUT=30
Environment=GUNICORN_LOG_LEVEL=info

# 从文件加载额外的环境变量
EnvironmentFile=-/var/www/tempmail/.env
EnvironmentFile=-/etc/tempmail/tempmail.conf

# 运行时目录配置
RuntimeDirectory=tempmail
RuntimeDirectoryMode=0750
StateDirectory=tempmail
StateDirectoryMode=0750
LogsDirectory=tempmail
LogsDirectoryMode=0750
CacheDirectory=tempmail
CacheDirectoryMode=0750

# 服务启动命令
ExecStart=/var/www/tempmail/venv/bin/gunicorn \
    --config /var/www/tempmail/gunicorn.conf.py \
    --pid /run/tempmail/gunicorn.pid \
    app:app

# 服务重载命令
ExecReload=/bin/kill -s HUP $MAINPID

# 服务停止配置
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

# 重启策略
Restart=always
RestartSec=5
StartLimitInterval=60
StartLimitBurst=3

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096
LimitCORE=0
MemoryMax=512M
TasksMax=1024

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true
RemoveIPC=true
PrivateTmp=true
PrivateDevices=true
ProtectHostname=true
ProtectClock=true
ProtectKernelLogs=true
RestrictNamespaces=true
LockPersonality=true
RestrictAddressFamilies=AF_UNIX AF_INET AF_INET6

# 文件系统访问权限
ReadWritePaths=/var/www/tempmail/logs
ReadWritePaths=/var/www/tempmail/database
ReadWritePaths=/var/www/tempmail/run
ReadWritePaths=/run/tempmail
ReadWritePaths=/var/lib/tempmail
ReadWritePaths=/var/cache/tempmail
ReadOnlyPaths=/var/www/tempmail

# 系统调用过滤
SystemCallFilter=@system-service
SystemCallFilter=~@debug @mount @cpu-emulation @obsolete @privileged @reboot @swap @raw-io

# 能力限制
CapabilityBoundingSet=
AmbientCapabilities=

# 网络配置
IPAddressDeny=any
IPAddressAllow=localhost
IPAddressAllow=*********/8
IPAddressAllow=::1/128

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=tempmail

[Install]
WantedBy=multi-user.target
