[Unit]
Description=Temporary Email System - Simple Configuration
After=network.target
Wants=network.target

[Service]
# 基本配置
Type=simple
User=tempmail
Group=tempmail
WorkingDirectory=/var/www/tempmail

# 环境变量
Environment=PATH=/var/www/tempmail/venv/bin:/usr/local/bin:/usr/bin:/bin
Environment=PYTHONPATH=/var/www/tempmail
Environment=FLASK_ENV=production
Environment=FLASK_DEBUG=0

# 从文件加载环境变量
EnvironmentFile=-/var/www/tempmail/.env

# 启动命令
ExecStart=/var/www/tempmail/venv/bin/gunicorn -c gunicorn.conf.py app:app

# 重启策略
Restart=always
RestartSec=3

# 基本资源限制
LimitNOFILE=65536

# 日志
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
