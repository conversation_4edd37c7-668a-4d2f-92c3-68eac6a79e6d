# 临时邮箱系统 Dockerfile
# 基于Python 3.11的官方镜像

FROM python:3.11-slim

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    FLASK_APP=app.py \
    FLASK_ENV=production \
    FLASK_DEBUG=0

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    sqlite3 \
    nodejs \
    npm \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN groupadd -r tempmail && useradd -r -g tempmail tempmail

# 复制依赖文件
COPY requirements.txt package.json ./

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 安装Node.js依赖
RUN npm install

# 复制应用程序代码
COPY . .

# 构建前端资源
RUN npm run build-css

# 创建必要的目录
RUN mkdir -p logs database run static && \
    chown -R tempmail:tempmail /app

# 设置权限
RUN chmod +x scripts/*.sh

# 切换到非root用户
USER tempmail

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 启动命令
CMD ["gunicorn", "-c", "gunicorn.conf.py", "app:app"]
