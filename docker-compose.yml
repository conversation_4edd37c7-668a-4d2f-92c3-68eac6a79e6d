version: '3.8'

services:
  # 临时邮箱应用程序
  tempmail:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: tempmail-app
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - FLASK_ENV=production
      - FLASK_DEBUG=0
      - DATABASE_TYPE=sqlite
      - DATABASE_PATH=/app/database/tempmail.db
      - DOMAIN_NAME=${DOMAIN_NAME:-localhost}
      - EMAIL_EXPIRATION_HOURS=24
      - MONITORING_LEVEL=basic
      - MONITORING_EMAIL_ENABLED=true
      - MONITORING_USE_INTERNAL_MAIL=true
    volumes:
      - tempmail_data:/app/database
      - tempmail_logs:/app/logs
      - ./config/env/.env.production:/app/.env:ro
    networks:
      - tempmail-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.tempmail.rule=Host(`${DOMAIN_NAME:-localhost}`)"
      - "traefik.http.services.tempmail.loadbalancer.server.port=8080"

  # Redis缓存 (可选)
  redis:
    image: redis:7-alpine
    container_name: tempmail-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - tempmail-network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: tempmail-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx/tempmail.conf:/etc/nginx/conf.d/default.conf:ro
      - ./static:/var/www/tempmail/static:ro
      - nginx_logs:/var/log/nginx
      - ssl_certs:/etc/ssl/certs:ro
    networks:
      - tempmail-network
    depends_on:
      - tempmail
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 监控服务 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: tempmail-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./config/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - tempmail-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana仪表板 (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: tempmail-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/monitoring/grafana:/etc/grafana/provisioning
    networks:
      - tempmail-network
    depends_on:
      - prometheus

networks:
  tempmail-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  tempmail_data:
    driver: local
  tempmail_logs:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local
  ssl_certs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
