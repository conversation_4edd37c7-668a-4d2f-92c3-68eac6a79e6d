# Gunicorn 生产环境配置文件
# 使用方法: gunicorn -c gunicorn.conf.py app:app

import os
import multiprocessing
from pathlib import Path

# 获取项目根目录
PROJECT_ROOT = Path(__file__).parent.absolute()

# 服务器套接字配置
# 支持环境变量覆盖，默认绑定到所有接口的8080端口
bind = os.getenv("GUNICORN_BIND", "0.0.0.0:8080")
backlog = int(os.getenv("GUNICORN_BACKLOG", "2048"))

# 工作进程配置
# 自动根据CPU核心数计算，或使用环境变量指定
cpu_count = multiprocessing.cpu_count()
workers = int(os.getenv("GUNICORN_WORKERS", min(cpu_count * 2 + 1, 4)))  # 最多4个工作进程
worker_class = os.getenv("GUNICORN_WORKER_CLASS", "sync")
worker_connections = int(os.getenv("GUNICORN_WORKER_CONNECTIONS", "1000"))
timeout = int(os.getenv("GUNICORN_TIMEOUT", "30"))
keepalive = int(os.getenv("GUNICORN_KEEPALIVE", "2"))

# 最大请求数（防止内存泄漏）
max_requests = int(os.getenv("GUNICORN_MAX_REQUESTS", "1000"))
max_requests_jitter = int(os.getenv("GUNICORN_MAX_REQUESTS_JITTER", "50"))

# 进程管理
preload_app = os.getenv("GUNICORN_PRELOAD_APP", "true").lower() == "true"
daemon = False  # 在systemd下不使用daemon模式
pidfile = os.getenv("GUNICORN_PIDFILE", str(PROJECT_ROOT / "run" / "gunicorn.pid"))

# 确保PID文件目录存在
os.makedirs(os.path.dirname(pidfile), exist_ok=True)

# 日志配置
log_dir = PROJECT_ROOT / "logs"
log_dir.mkdir(exist_ok=True)

accesslog = os.getenv("GUNICORN_ACCESS_LOG", str(log_dir / "gunicorn_access.log"))
errorlog = os.getenv("GUNICORN_ERROR_LOG", str(log_dir / "gunicorn_error.log"))
loglevel = os.getenv("GUNICORN_LOG_LEVEL", "info")
access_log_format = os.getenv(
    "GUNICORN_ACCESS_LOG_FORMAT",
    '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'
)

# 安全配置
limit_request_line = int(os.getenv("GUNICORN_LIMIT_REQUEST_LINE", "4094"))
limit_request_fields = int(os.getenv("GUNICORN_LIMIT_REQUEST_FIELDS", "100"))
limit_request_field_size = int(os.getenv("GUNICORN_LIMIT_REQUEST_FIELD_SIZE", "8190"))

# 性能优化
# 使用内存文件系统（如果可用），否则使用临时目录
worker_tmp_dir = "/dev/shm" if os.path.exists("/dev/shm") else None

# 环境变量
raw_env = [
    'FLASK_ENV=production',
    'FLASK_DEBUG=0',
    f'PYTHONPATH={PROJECT_ROOT}'
]

# SSL配置（如果需要HTTPS直接终止）
# 通常建议在Nginx中处理SSL，这里仅作为备选方案
keyfile = os.getenv("GUNICORN_KEYFILE")
certfile = os.getenv("GUNICORN_CERTFILE")
ssl_version = 2  # SSLv23
ciphers = "TLSv1"

# 启动时的钩子函数
def on_starting(server):
    """服务器启动时执行"""
    server.log.info("=== Gunicorn 临时邮箱服务器启动 ===")
    server.log.info(f"项目根目录: {PROJECT_ROOT}")
    server.log.info(f"绑定地址: {bind}")
    server.log.info(f"工作进程数: {workers}")
    server.log.info(f"PID文件: {pidfile}")
    server.log.info("================================")

def on_reload(server):
    """重新加载时执行"""
    server.log.info("Gunicorn 服务器正在重新加载配置...")

def worker_int(worker):
    """工作进程中断时执行"""
    worker.log.info("工作进程 %s 收到中断信号", worker.pid)

def pre_fork(server, worker):
    """工作进程分叉前执行"""
    server.log.info("准备启动工作进程 %s", worker.pid)

def post_fork(server, worker):
    """工作进程分叉后执行"""
    worker.log.info("工作进程 %s 已成功启动", worker.pid)

    # 在工作进程中设置进程标题
    try:
        import setproctitle
        setproctitle.setproctitle(f"tempmail-worker-{worker.pid}")
    except ImportError:
        pass

def worker_abort(worker):
    """工作进程异常终止时执行"""
    worker.log.error("工作进程 %s 异常终止", worker.pid)

def on_exit(server):
    """服务器退出时执行"""
    server.log.info("Gunicorn 服务器正在关闭...")

    # 清理PID文件
    try:
        if os.path.exists(pidfile):
            os.remove(pidfile)
            server.log.info("已清理PID文件: %s", pidfile)
    except Exception as e:
        server.log.error("清理PID文件失败: %s", e)

# 配置验证函数
def validate_config():
    """验证配置的有效性"""
    errors = []

    # 检查工作进程数
    if workers < 1:
        errors.append("工作进程数必须大于0")

    # 检查超时设置
    if timeout < 1:
        errors.append("超时时间必须大于0秒")

    # 检查日志目录
    if not os.path.exists(os.path.dirname(accesslog)):
        errors.append(f"访问日志目录不存在: {os.path.dirname(accesslog)}")

    if not os.path.exists(os.path.dirname(errorlog)):
        errors.append(f"错误日志目录不存在: {os.path.dirname(errorlog)}")

    if errors:
        raise ValueError("配置验证失败:\n" + "\n".join(f"- {error}" for error in errors))

# 在导入时验证配置
try:
    validate_config()
except ValueError as e:
    print(f"Gunicorn配置错误: {e}")
    exit(1)
