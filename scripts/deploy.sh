#!/bin/bash

# 临时邮箱系统自动化部署脚本
# 使用方法: ./scripts/deploy.sh [选项]

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="/tmp/tempmail_deploy_$(date +%Y%m%d_%H%M%S).log"

# 默认配置
DEFAULT_USER="tempmail"
DEFAULT_GROUP="tempmail"
DEFAULT_INSTALL_DIR="/var/www/tempmail"
DEFAULT_DOMAIN="your-domain.com"
DEFAULT_ENV="production"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
}

log_info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO:${NC} $1" | tee -a "$LOG_FILE"
}

# 显示帮助信息
show_help() {
    cat << EOF
临时邮箱系统部署脚本

使用方法:
    $0 [选项]

选项:
    -u, --user USER         运行用户 (默认: $DEFAULT_USER)
    -g, --group GROUP       运行组 (默认: $DEFAULT_GROUP)
    -d, --dir DIR           安装目录 (默认: $DEFAULT_INSTALL_DIR)
    -D, --domain DOMAIN     域名 (默认: $DEFAULT_DOMAIN)
    -e, --env ENV           环境 (development/production, 默认: $DEFAULT_ENV)
    --skip-system           跳过系统包安装
    --skip-user             跳过用户创建
    --skip-nginx            跳过Nginx配置
    --skip-systemd          跳过Systemd服务配置
    --dry-run               仅显示将要执行的操作
    -h, --help              显示此帮助信息

示例:
    $0 --domain example.com --env production
    $0 --user myuser --dir /opt/tempmail --skip-nginx
    $0 --dry-run

EOF
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$ID
        OS_VERSION=$VERSION_ID
    else
        log_error "无法检测操作系统"
        exit 1
    fi
    
    log_info "检测到操作系统: $OS $OS_VERSION"
}

# 安装系统依赖
install_system_packages() {
    log "安装系统依赖包..."
    
    case $OS in
        ubuntu|debian)
            apt-get update
            apt-get install -y \
                python3 \
                python3-pip \
                python3-venv \
                nginx \
                sqlite3 \
                curl \
                wget \
                git \
                build-essential \
                python3-dev \
                libssl-dev \
                libffi-dev \
                nodejs \
                npm
            ;;
        centos|rhel|fedora)
            if command -v dnf &> /dev/null; then
                dnf install -y \
                    python3 \
                    python3-pip \
                    nginx \
                    sqlite \
                    curl \
                    wget \
                    git \
                    gcc \
                    python3-devel \
                    openssl-devel \
                    libffi-devel \
                    nodejs \
                    npm
            else
                yum install -y \
                    python3 \
                    python3-pip \
                    nginx \
                    sqlite \
                    curl \
                    wget \
                    git \
                    gcc \
                    python3-devel \
                    openssl-devel \
                    libffi-devel \
                    nodejs \
                    npm
            fi
            ;;
        *)
            log_error "不支持的操作系统: $OS"
            exit 1
            ;;
    esac
    
    log "系统依赖包安装完成"
}

# 创建用户和组
create_user() {
    log "创建用户和组..."
    
    if ! getent group "$GROUP" > /dev/null 2>&1; then
        groupadd "$GROUP"
        log_info "创建组: $GROUP"
    else
        log_info "组已存在: $GROUP"
    fi
    
    if ! getent passwd "$USER" > /dev/null 2>&1; then
        useradd -r -g "$GROUP" -d "$INSTALL_DIR" -s /bin/bash "$USER"
        log_info "创建用户: $USER"
    else
        log_info "用户已存在: $USER"
    fi
}

# 创建目录结构
create_directories() {
    log "创建目录结构..."
    
    local dirs=(
        "$INSTALL_DIR"
        "$INSTALL_DIR/logs"
        "$INSTALL_DIR/database"
        "$INSTALL_DIR/run"
        "$INSTALL_DIR/static"
        "$INSTALL_DIR/templates"
        "/etc/tempmail"
        "/var/log/tempmail"
        "/var/lib/tempmail"
        "/var/cache/tempmail"
    )
    
    for dir in "${dirs[@]}"; do
        mkdir -p "$dir"
        chown "$USER:$GROUP" "$dir"
        chmod 755 "$dir"
        log_info "创建目录: $dir"
    done
}

# 复制应用文件
copy_application() {
    log "复制应用程序文件..."
    # 如果源和目标目录相同，跳过复制，避免自复制报错
    if [[ "$PROJECT_ROOT" == "$INSTALL_DIR" ]]; then
        log_warn "源目录和目标目录相同，跳过文件复制"
    else
        # 复制主要文件
        cp -r "$PROJECT_ROOT"/* "$INSTALL_DIR/"
    fi
    # 设置权限
    chown -R "$USER:$GROUP" "$INSTALL_DIR"
    find "$INSTALL_DIR" -type f -name "*.py" -exec chmod 644 {} \;
    find "$INSTALL_DIR" -type f -name "*.sh" -exec chmod 755 {} \;
    log "应用程序文件复制完成"
}

# 设置Python环境
setup_python_env() {
    log "设置Python虚拟环境..."
    
    cd "$INSTALL_DIR"
    
    # 创建虚拟环境
    sudo -u "$USER" python3 -m venv venv
    
    # 激活虚拟环境并安装依赖
    sudo -u "$USER" bash -c "
        source venv/bin/activate
        pip install --upgrade pip
        pip install -r requirements.txt
    "
    
    log "Python环境设置完成"
}

# 设置前端环境
setup_frontend() {
    log "设置前端环境..."
    
    cd "$INSTALL_DIR"
    
    # 安装Node.js依赖
    sudo -u "$USER" npm install
    
    # 构建CSS
    sudo -u "$USER" npm run build-css
    
    log "前端环境设置完成"
}

# 配置环境变量
configure_environment() {
    log "配置环境变量..."
    
    local env_file="$INSTALL_DIR/.env"
    local template_file="$INSTALL_DIR/config/env/.env.$ENV"
    
    if [[ -f "$template_file" ]]; then
        cp "$template_file" "$env_file"
        
        # 替换配置值
        sed -i "s/your-domain.com/$DOMAIN/g" "$env_file"
        sed -i "s|/var/www/tempmail|$INSTALL_DIR|g" "$env_file"
        
        # 生成安全密钥
        local secret_key=$(python3 -c "import secrets; print(secrets.token_hex(32))")
        sed -i "s/CHANGE_THIS_TO_A_SECURE_SECRET_KEY_IN_PRODUCTION/$secret_key/g" "$env_file"
        
        chown "$USER:$GROUP" "$env_file"
        chmod 600 "$env_file"
        
        log_info "环境变量配置完成: $env_file"
    else
        log_warn "环境变量模板文件不存在: $template_file, 跳过配置"
    fi
}

# 初始化数据库
initialize_database() {
    log "初始化数据库..."
    
    cd "$INSTALL_DIR"
    
    sudo -u "$USER" bash -c "
        source venv/bin/activate
        python -c '
from app import app
with app.app_context():
    from app import init_db_schema
    init_db_schema()
    print(\"数据库初始化完成\")
'
    "
    
    log "数据库初始化完成"
}

# 主函数
main() {
    local USER="$DEFAULT_USER"
    local GROUP="$DEFAULT_GROUP"
    local INSTALL_DIR="$DEFAULT_INSTALL_DIR"
    local DOMAIN="$DEFAULT_DOMAIN"
    local ENV="$DEFAULT_ENV"
    local SKIP_SYSTEM=false
    local SKIP_USER=false
    local SKIP_NGINX=false
    local SKIP_SYSTEMD=false
    local DRY_RUN=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -u|--user)
                USER="$2"
                shift 2
                ;;
            -g|--group)
                GROUP="$2"
                shift 2
                ;;
            -d|--dir)
                INSTALL_DIR="$2"
                shift 2
                ;;
            -D|--domain)
                DOMAIN="$2"
                shift 2
                ;;
            -e|--env)
                ENV="$2"
                shift 2
                ;;
            --skip-system)
                SKIP_SYSTEM=true
                shift
                ;;
            --skip-user)
                SKIP_USER=true
                shift
                ;;
            --skip-nginx)
                SKIP_NGINX=true
                shift
                ;;
            --skip-systemd)
                SKIP_SYSTEMD=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示配置信息
    log "=== 部署配置 ==="
    log_info "用户: $USER"
    log_info "组: $GROUP"
    log_info "安装目录: $INSTALL_DIR"
    log_info "域名: $DOMAIN"
    log_info "环境: $ENV"
    log_info "日志文件: $LOG_FILE"
    log "================="
    
    if [[ "$DRY_RUN" == true ]]; then
        log_warn "这是一次试运行，不会执行实际操作"
        exit 0
    fi
    
    # 执行部署步骤
    check_root
    detect_os
    
    if [[ "$SKIP_SYSTEM" != true ]]; then
        install_system_packages
    fi
    
    if [[ "$SKIP_USER" != true ]]; then
        create_user
    fi
    
    create_directories
    copy_application
    setup_python_env
    setup_frontend
    #configure_environment
    initialize_database
    configure_nginx
    configure_systemd

    log "=== 部署完成 ==="
    log_info "应用程序已安装到: $INSTALL_DIR"
    log_info "服务状态: $(systemctl is-active tempmail 2>/dev/null || echo '未配置')"
    log_info "访问地址: http://$DOMAIN (如果配置了SSL则为https://$DOMAIN)"
    log_info "日志文件: $LOG_FILE"
    log "================="
}

# 配置Nginx
configure_nginx() {
    if [[ "$SKIP_NGINX" == true ]]; then
        return
    fi

    log "配置Nginx..."

    local nginx_config="/etc/nginx/sites-available/tempmail"
    local nginx_enabled="/etc/nginx/sites-enabled/tempmail"

    # 复制Nginx配置
    cp "$INSTALL_DIR/config/nginx/tempmail.conf" "$nginx_config"

    # 替换域名
    sed -i "s/your-domain.com/$DOMAIN/g" "$nginx_config"
    sed -i "s|/var/www/tempmail|$INSTALL_DIR|g" "$nginx_config"

    # 启用站点
    ln -sf "$nginx_config" "$nginx_enabled"

    # 测试配置
    nginx -t

    # 重启Nginx
    systemctl restart nginx
    systemctl enable nginx

    log "Nginx配置完成"
}

# 配置Systemd服务
configure_systemd() {
    if [[ "$SKIP_SYSTEMD" == true ]]; then
        return
    fi

    log "配置Systemd服务..."

    local service_file="/etc/systemd/system/tempmail.service"

    # 复制服务文件
    cp "$INSTALL_DIR/config/systemd/tempmail.service" "$service_file"

    # 替换路径和用户
    sed -i "s/User=tempmail/User=$USER/g" "$service_file"
    sed -i "s/Group=tempmail/Group=$GROUP/g" "$service_file"
    sed -i "s|/var/www/tempmail|$INSTALL_DIR|g" "$service_file"

    # 重新加载systemd
    systemctl daemon-reload

    # 启用并启动服务
    systemctl enable tempmail
    systemctl start tempmail

    log "Systemd服务配置完成"
}

# 运行主函数
main "$@"
