#!/bin/bash

# Docker部署脚本
# 使用方法: ./scripts/docker-deploy.sh [选项]

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 默认配置
DEFAULT_ENV="production"
DEFAULT_DOMAIN="localhost"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

log_info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Docker部署脚本

使用方法:
    $0 [选项] [命令]

选项:
    -e, --env ENV           环境 (development/production, 默认: $DEFAULT_ENV)
    -d, --domain DOMAIN     域名 (默认: $DEFAULT_DOMAIN)
    --build                 强制重新构建镜像
    --no-cache              构建时不使用缓存
    -h, --help              显示此帮助信息

命令:
    up                      启动服务 (默认)
    down                    停止服务
    restart                 重启服务
    logs                    查看日志
    status                  查看状态
    build                   构建镜像
    clean                   清理资源

示例:
    $0 --env production --domain example.com up
    $0 down
    $0 logs tempmail

EOF
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        log_info "请先安装Docker: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装"
        log_info "请先安装Docker Compose: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    # 检查Docker服务是否运行
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行"
        log_info "请启动Docker服务"
        exit 1
    fi
}

# 准备环境
prepare_environment() {
    log "准备Docker环境..."
    
    cd "$PROJECT_ROOT"
    
    # 创建必要的目录
    mkdir -p logs database run
    
    # 设置环境变量文件
    if [[ ! -f .env ]]; then
        if [[ -f "config/env/.env.$ENV" ]]; then
            cp "config/env/.env.$ENV" .env
            log_info "复制环境变量文件: .env.$ENV -> .env"
        else
            log_warn "环境变量文件不存在: config/env/.env.$ENV"
        fi
    fi
    
    # 替换域名
    if [[ -f .env ]]; then
        sed -i "s/your-domain.com/$DOMAIN/g" .env
        log_info "更新域名配置: $DOMAIN"
    fi
    
    # 导出环境变量供docker-compose使用
    export DOMAIN_NAME="$DOMAIN"
}

# 构建镜像
build_images() {
    log "构建Docker镜像..."
    
    local build_args=""
    if [[ "${BUILD_NO_CACHE:-false}" == "true" ]]; then
        build_args="--no-cache"
    fi
    
    if [[ "$ENV" == "production" ]]; then
        docker-compose -f docker-compose.prod.yml build $build_args
    else
        docker-compose build $build_args
    fi
    
    log "镜像构建完成"
}

# 启动服务
start_services() {
    log "启动Docker服务..."
    
    if [[ "$ENV" == "production" ]]; then
        docker-compose -f docker-compose.prod.yml up -d
    else
        docker-compose up -d
    fi
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    show_status
}

# 停止服务
stop_services() {
    log "停止Docker服务..."
    
    if [[ "$ENV" == "production" ]]; then
        docker-compose -f docker-compose.prod.yml down
    else
        docker-compose down
    fi
    
    log "服务已停止"
}

# 重启服务
restart_services() {
    log "重启Docker服务..."
    stop_services
    start_services
}

# 查看日志
show_logs() {
    local service="${1:-}"
    local follow_flag=""
    
    if [[ "${2:-}" == "--follow" ]] || [[ "${2:-}" == "-f" ]]; then
        follow_flag="-f"
    fi
    
    if [[ "$ENV" == "production" ]]; then
        docker-compose -f docker-compose.prod.yml logs $follow_flag $service
    else
        docker-compose logs $follow_flag $service
    fi
}

# 查看状态
show_status() {
    log "Docker服务状态:"
    echo "----------------------------------------"
    
    if [[ "$ENV" == "production" ]]; then
        docker-compose -f docker-compose.prod.yml ps
    else
        docker-compose ps
    fi
    
    echo "----------------------------------------"
    
    # 检查健康状态
    log_info "健康检查:"
    local containers
    if [[ "$ENV" == "production" ]]; then
        containers=$(docker-compose -f docker-compose.prod.yml ps -q)
    else
        containers=$(docker-compose ps -q)
    fi
    
    for container in $containers; do
        if [[ -n "$container" ]]; then
            local name=$(docker inspect --format='{{.Name}}' "$container" | sed 's/^\/*//')
            local health=$(docker inspect --format='{{.State.Health.Status}}' "$container" 2>/dev/null || echo "no-healthcheck")
            echo "  $name: $health"
        fi
    done
}

# 清理资源
clean_resources() {
    log "清理Docker资源..."
    
    # 停止服务
    stop_services
    
    # 删除镜像
    log_info "删除镜像..."
    docker images | grep tempmail | awk '{print $3}' | xargs -r docker rmi -f
    
    # 清理未使用的资源
    log_info "清理未使用的资源..."
    docker system prune -f
    
    log "资源清理完成"
}

# 主函数
main() {
    local ENV="$DEFAULT_ENV"
    local DOMAIN="$DEFAULT_DOMAIN"
    local COMMAND="up"
    local FORCE_BUILD=false
    local BUILD_NO_CACHE=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--env)
                ENV="$2"
                shift 2
                ;;
            -d|--domain)
                DOMAIN="$2"
                shift 2
                ;;
            --build)
                FORCE_BUILD=true
                shift
                ;;
            --no-cache)
                BUILD_NO_CACHE=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            up|down|restart|logs|status|build|clean)
                COMMAND="$1"
                shift
                ;;
            *)
                if [[ "$COMMAND" == "logs" ]]; then
                    # 对于logs命令，剩余参数作为服务名和选项
                    break
                else
                    log_error "未知参数: $1"
                    show_help
                    exit 1
                fi
                ;;
        esac
    done
    
    # 显示配置信息
    log "=== Docker部署配置 ==="
    log_info "环境: $ENV"
    log_info "域名: $DOMAIN"
    log_info "命令: $COMMAND"
    log "======================="
    
    # 检查Docker环境
    check_docker
    
    # 准备环境
    prepare_environment
    
    # 导出变量供子函数使用
    export ENV DOMAIN BUILD_NO_CACHE
    
    # 执行命令
    case $COMMAND in
        up)
            if [[ "$FORCE_BUILD" == true ]]; then
                build_images
            fi
            start_services
            ;;
        down)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            show_logs "$@"
            ;;
        status)
            show_status
            ;;
        build)
            build_images
            ;;
        clean)
            clean_resources
            ;;
        *)
            log_error "未知命令: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
