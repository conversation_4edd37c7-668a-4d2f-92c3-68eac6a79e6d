#!/bin/bash

# 临时邮箱系统服务管理脚本
# 使用方法: ./scripts/manage-service.sh [命令]

set -euo pipefail

# 配置
SERVICE_NAME="tempmail"
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
LOG_FILE="/tmp/tempmail_service_$(date +%Y%m%d_%H%M%S).log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
}

log_info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO:${NC} $1" | tee -a "$LOG_FILE"
}

# 显示帮助信息
show_help() {
    cat << EOF
临时邮箱系统服务管理脚本

使用方法:
    $0 [命令]

命令:
    start           启动服务
    stop            停止服务
    restart         重启服务
    reload          重新加载配置
    status          查看服务状态
    logs            查看服务日志
    enable          启用服务（开机自启）
    disable         禁用服务
    install         安装服务文件
    uninstall       卸载服务文件
    health          健康检查
    backup          备份配置
    restore         恢复配置
    update          更新服务配置
    -h, --help      显示此帮助信息

示例:
    $0 start
    $0 status
    $0 logs --follow

EOF
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此操作需要root权限"
        log_info "请使用: sudo $0 $*"
        exit 1
    fi
}

# 检查服务是否存在
check_service_exists() {
    if ! systemctl list-unit-files | grep -q "^$SERVICE_NAME.service"; then
        log_error "服务 $SERVICE_NAME 不存在"
        log_info "请先运行: $0 install"
        exit 1
    fi
}

# 启动服务
start_service() {
    log "启动服务 $SERVICE_NAME..."
    check_service_exists
    
    systemctl start "$SERVICE_NAME"
    sleep 2
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log "服务启动成功"
        show_status
    else
        log_error "服务启动失败"
        log_info "查看日志: $0 logs"
        exit 1
    fi
}

# 停止服务
stop_service() {
    log "停止服务 $SERVICE_NAME..."
    check_service_exists
    
    systemctl stop "$SERVICE_NAME"
    sleep 2
    
    if ! systemctl is-active --quiet "$SERVICE_NAME"; then
        log "服务停止成功"
    else
        log_error "服务停止失败"
        exit 1
    fi
}

# 重启服务
restart_service() {
    log "重启服务 $SERVICE_NAME..."
    check_service_exists
    
    systemctl restart "$SERVICE_NAME"
    sleep 2
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log "服务重启成功"
        show_status
    else
        log_error "服务重启失败"
        log_info "查看日志: $0 logs"
        exit 1
    fi
}

# 重新加载配置
reload_service() {
    log "重新加载服务配置..."
    check_service_exists
    
    systemctl reload "$SERVICE_NAME" 2>/dev/null || {
        log_warn "服务不支持reload，使用restart"
        restart_service
        return
    }
    
    log "配置重新加载成功"
}

# 查看服务状态
show_status() {
    log "服务状态信息:"
    echo "----------------------------------------"
    systemctl status "$SERVICE_NAME" --no-pager -l
    echo "----------------------------------------"
    
    # 显示端口监听状态
    log_info "端口监听状态:"
    netstat -tlnp | grep :8080 || log_warn "端口8080未监听"
    
    # 显示进程信息
    log_info "进程信息:"
    pgrep -f gunicorn | head -5 | while read pid; do
        ps -p "$pid" -o pid,ppid,cmd --no-headers 2>/dev/null || true
    done
}

# 查看日志
show_logs() {
    local follow_flag=""
    if [[ "${1:-}" == "--follow" ]] || [[ "${1:-}" == "-f" ]]; then
        follow_flag="-f"
    fi
    
    log "查看服务日志..."
    echo "----------------------------------------"
    journalctl -u "$SERVICE_NAME" --no-pager -l $follow_flag
}

# 启用服务
enable_service() {
    log "启用服务 $SERVICE_NAME (开机自启)..."
    check_service_exists
    
    systemctl enable "$SERVICE_NAME"
    log "服务已启用"
}

# 禁用服务
disable_service() {
    log "禁用服务 $SERVICE_NAME..."
    check_service_exists
    
    systemctl disable "$SERVICE_NAME"
    log "服务已禁用"
}

# 安装服务文件
install_service() {
    log "安装服务文件..."
    
    local service_file="$PROJECT_ROOT/config/systemd/tempmail.service"
    local target_file="/etc/systemd/system/$SERVICE_NAME.service"
    
    if [[ ! -f "$service_file" ]]; then
        log_error "服务文件不存在: $service_file"
        exit 1
    fi
    
    cp "$service_file" "$target_file"
    systemctl daemon-reload
    
    log "服务文件安装完成: $target_file"
    log_info "使用 '$0 enable' 启用服务"
    log_info "使用 '$0 start' 启动服务"
}

# 卸载服务文件
uninstall_service() {
    log "卸载服务文件..."
    
    local target_file="/etc/systemd/system/$SERVICE_NAME.service"
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_warn "服务正在运行，先停止服务"
        stop_service
    fi
    
    if systemctl is-enabled --quiet "$SERVICE_NAME"; then
        log_warn "服务已启用，先禁用服务"
        disable_service
    fi
    
    rm -f "$target_file"
    systemctl daemon-reload
    
    log "服务文件卸载完成"
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    local health_url="http://localhost:8080/health"
    local status_code
    
    # 检查服务状态
    if ! systemctl is-active --quiet "$SERVICE_NAME"; then
        log_error "服务未运行"
        return 1
    fi
    
    # 检查HTTP响应
    if command -v curl &> /dev/null; then
        status_code=$(curl -s -o /dev/null -w "%{http_code}" "$health_url" || echo "000")
        if [[ "$status_code" == "200" ]]; then
            log "健康检查通过 (HTTP $status_code)"
        else
            log_error "健康检查失败 (HTTP $status_code)"
            return 1
        fi
    else
        log_warn "curl未安装，跳过HTTP健康检查"
    fi
    
    # 检查日志中的错误
    local error_count
    error_count=$(journalctl -u "$SERVICE_NAME" --since "5 minutes ago" | grep -i error | wc -l)
    if [[ "$error_count" -gt 0 ]]; then
        log_warn "最近5分钟内发现 $error_count 个错误"
    else
        log_info "最近5分钟内无错误日志"
    fi
    
    log "健康检查完成"
}

# 备份配置
backup_config() {
    log "备份配置文件..."
    
    local backup_dir="/tmp/tempmail_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份服务文件
    if [[ -f "/etc/systemd/system/$SERVICE_NAME.service" ]]; then
        cp "/etc/systemd/system/$SERVICE_NAME.service" "$backup_dir/"
    fi
    
    # 备份Nginx配置
    if [[ -f "/etc/nginx/sites-available/tempmail" ]]; then
        cp "/etc/nginx/sites-available/tempmail" "$backup_dir/"
    fi
    
    # 备份环境变量
    if [[ -f "$PROJECT_ROOT/.env" ]]; then
        cp "$PROJECT_ROOT/.env" "$backup_dir/"
    fi
    
    log "配置备份完成: $backup_dir"
}

# 主函数
main() {
    case "${1:-}" in
        start)
            check_root
            start_service
            ;;
        stop)
            check_root
            stop_service
            ;;
        restart)
            check_root
            restart_service
            ;;
        reload)
            check_root
            reload_service
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs "${2:-}"
            ;;
        enable)
            check_root
            enable_service
            ;;
        disable)
            check_root
            disable_service
            ;;
        install)
            check_root
            install_service
            ;;
        uninstall)
            check_root
            uninstall_service
            ;;
        health)
            health_check
            ;;
        backup)
            check_root
            backup_config
            ;;
        -h|--help|help)
            show_help
            ;;
        "")
            log_error "请指定命令"
            show_help
            exit 1
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
